# 修复组件路径和重构课程详情页

## 修改时间

2025-06-19

## 问题描述

1. CourseSeriesDetail 和 HomeworkList 组件的位置已经发生迁移，导致导入路径错误
2. course-series-list.vue 页面需要重构，显示完整的课程详情页面，包括：
   - 顶部标签切换（我的作业、1000 词单词营、Timed reading 阅读营）
   - 课程信息卡片
   - 今日待学部分
   - 学习目录部分（显示所有课节）

## 修改内容

### 1. 修复组件导入路径

**文件**: `pages/study/study.vue`

**修改前**:

```typescript
import CourseSeriesDetail from "@/components/course-series-detail/course-series-detail.vue";
import HomeworkList from "@/components/homework-list/homework-list.vue";
```

**修改后**:

```typescript
import CourseSeriesDetail from "@/pages/course-series-detail/course-series-detail.vue";
import HomeworkList from "@/pages/study/homework-list.vue";
```

### 2. 重构 course-series-list.vue 页面

**文件**: `pages/study/course-series-list.vue`

#### 2.1 模板结构重构

- 添加顶部标签切换栏
- 重新设计课程信息卡片，包含 Level 选择器
- 添加今日待学部分
- 添加学习目录部分，显示所有课节

#### 2.2 数据结构调整

```typescript
// 新增响应式数据
const seriesDetail = ref<CourseSeriesDetail | null>(null);
const currentCourse = ref<CourseInfo | null>(null);
const currentCourseIndex = ref(0);
const todayLesson = ref<LessonDetailInfo | null>(null);
const activeTab = ref("homework");
const seriesId = ref(1);

// 标签数据
const tabs = ref<Tab[]>([
  { id: "homework", name: "我的作业" },
  { id: "words", name: "1000词单词营" },
  { id: "reading", name: "Timed reading阅读营" },
]);
```

#### 2.3 功能函数重构

- `fetchSeriesDetail()`: 获取系列详情数据
- `handleTabClick()`: 处理标签切换
- `onCourseChange()`: 处理课程切换
- `handleLessonClick()`: 处理课节点击
- `getLessonStatusClass()` 和 `getLessonStatusIcon()`: 获取课节状态

#### 2.4 样式重构

- 添加标签栏样式
- 重新设计课程信息卡片样式
- 添加今日待学和学习目录样式
- 统一课节卡片样式

### 3. 接口调用

页面会调用 `/wbclass/course/series/1/detail` 接口获取课程系列详情，包括：

- 系列基本信息
- 所有课程列表
- 每个课程的课节列表

## 数据库查询结果

从数据库查询可以看到：

- 系列 ID 为 1 的"RE 阅读营"包含 3 个课程（1-3 册、4-6 册、7-9 册）
- 每个课程包含多个课节（Lesson 1-4）
- 课节有不同的解锁状态（autoUnlock 字段）

## 技术特点

1. **响应式设计**: 使用 Vue 3 Composition API
2. **数据驱动**: 通过接口获取真实数据
3. **状态管理**: 合理管理组件状态
4. **用户体验**: 流畅的交互和视觉效果
5. **可扩展性**: 支持多个标签和课程系列

## 注意事项

1. 页面支持通过 props 或 URL 参数传入 seriesId
2. 默认使用系列 ID 为 1（RE 阅读营）
3. 今日待学显示第一个未解锁的课节
4. 课节状态通过 autoUnlock 字段判断
5. 点击课节会跳转到课节详情页面

## 3. 修复 study.vue 页面的组件使用问题

**问题分析**：

- HomeworkList 组件需要 `homeworkList` 属性，但传递的是 `refreshTrigger`
- 缺少根据分类类型显示不同组件的逻辑
- 缺少作业数据获取功能

**修复内容**：

#### 3.1 更新导入

```typescript
import {
  CourseCategoryItem,
  getMyCourseCategoryList,
  getHomeworkList,
} from "@/api/homework";
import { computed, onMounted, reactive, ref } from "vue";
```

#### 3.2 更新状态管理

```typescript
interface HomeworkGroup {
  year: number;
  month: number;
  homeworkList: any[];
}

const state = reactive({
  categoryList: [] as CourseCategoryItem[],
  activeCategoryId: 0,
  homeworkList: [] as HomeworkGroup[],
  loading: false,
});

const currentCategory = computed(() => {
  return state.categoryList.find((item) => item.id === state.activeCategoryId);
});
```

#### 3.3 添加作业数据获取

```typescript
const fetchHomeworkList = async () => {
  try {
    state.loading = true;
    const res = await getHomeworkList({ pageNo: 1, pageSize: 500 });
    const data = res.data || res.getData();
    if (Array.isArray(data)) {
      state.homeworkList = data;
    }
  } catch (error) {
    console.error("获取作业列表失败:", error);
    state.homeworkList = [];
  } finally {
    state.loading = false;
  }
};
```

#### 3.4 更新模板逻辑

```vue
<!-- 作业列表 -->
<homework-list
  v-if="currentCategory?.type === 'homework'"
  ref="homeworkListRef"
  :homework-list="state.homeworkList"
/>

<!-- 课程系列详情 -->
<course-series-detail
  v-else-if="currentCategory?.type === 'series'"
  ref="courseSeriesDetailRef"
  :series-id="state.activeCategoryId"
/>
```

## 接口调用说明

### 1. 分类列表接口

- **接口**: `/wbclass/course/my-course-series`
- **返回**: `[{"id":0,"name":"我的作业","type":"homework"},{"id":1,"name":"RE 阅读营","type":"series"}]`

### 2. 作业列表接口

- **接口**: `/edusys/app-wbclass-homework/list`
- **返回**: `List<AppHomeworkMonthlyGroupRespVO>` (按月分组的作业列表)

### 3. 课程系列详情接口

- **接口**: `/wbclass/course/series/{seriesId}/detail`
- **返回**: 课程系列详情和所有课节信息

## 预期效果

修复后的页面将：

1. **正确显示标签切换**：根据接口返回的分类数据显示标签
2. **作业列表正常显示**：点击"我的作业"标签时显示按月分组的作业列表
3. **课程系列详情正常显示**：点击"RE 阅读营"等系列标签时显示课程详情页面
4. **解决空白问题**：页面不再显示空白，会根据选中的分类显示相应内容

## 4. 重构 course-series-list.vue 为列表页面

**问题分析**：

- 之前错误地将 course-series-list.vue 改成了详情页面
- study.vue 引入的是 course-series-list.vue，应该显示课程列表
- fetchSeriesDetail 函数没有被正确调用

**修复内容**：

#### 4.1 模板重构为列表形式

```vue
<template>
  <view class="course-series-list">
    <!-- 系列标题 -->
    <view class="series-header">
      <text class="series-title">{{ seriesDetail.seriesName }}</text>
      <text class="series-desc"
        >共{{ seriesDetail.allCourses?.length || 0 }}册课程</text
      >
    </view>

    <!-- 课程列表 -->
    <view class="course-list">
      <view v-for="course in seriesDetail.allCourses" class="course-item">
        <!-- 课程信息卡片 -->
        <view class="course-card">
          <!-- 课程信息 -->
        </view>

        <!-- 今日待学课节预览 -->
        <view class="today-lesson-preview">
          <!-- 今日待学信息 -->
        </view>
      </view>
    </view>
  </view>
</template>
```

#### 4.2 JavaScript 逻辑重构

```typescript
// 简化的响应式数据
const loading = ref(true);
const seriesDetail = ref<CourseSeriesDetail | null>(null);
const seriesId = ref(1);

// 新增的列表相关函数
const formatCourseProgress = (course: CourseInfo): string => {
  // 格式化单个课程的进度
};

const getCourseStatusText = (course: CourseInfo): string => {
  // 获取课程状态文本
};

const getTodayLesson = (course: CourseInfo): LessonDetailInfo | null => {
  // 获取课程的今日待学课节
};

const handleCourseClick = (course: CourseInfo) => {
  // 处理课程点击，跳转到课程详情页
};
```

#### 4.3 修复 fetchSeriesDetail 调用

```typescript
onMounted(() => {
  // 设置系列ID
  if (props.seriesId) {
    seriesId.value = props.seriesId;
  }

  console.log("course-series-list 组件已挂载，开始获取数据");
  fetchSeriesDetail(); // 确保函数被调用
});
```

#### 4.4 样式重构

- 移除详情页面相关样式（标签栏、Level 选择器等）
- 添加列表页面样式（系列标题、课程卡片、今日待学预览）
- 优化卡片布局和交互效果

## 测试建议

1. 检查接口返回数据格式是否正确
2. 确认用户有作业数据和课程系列数据
3. 测试标签切换功能
4. 验证数据加载和错误处理
5. **新增**：测试 course-series-list 组件是否正确调用 fetchSeriesDetail
6. **新增**：验证课程列表是否正确显示
7. **新增**：测试课程点击跳转功能
