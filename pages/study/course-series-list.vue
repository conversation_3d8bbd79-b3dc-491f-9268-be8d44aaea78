<template>
  <view class="course-series-detail">
    <view v-if="loading" class="loading-container">
      <text>加载中...</text>
    </view>

    <view v-else-if="currentCourse" class="detail-content">
      <!-- 当前课程信息卡片 -->
      <view class="course-info-card">
        <image
          class="course-cover"
          :src="currentCourse.coverUrl || '/static/images/default-course.png'"
          mode="aspectFill"
        />
        <view class="course-info">
          <text class="course-name">{{
            seriesDetail?.seriesName || currentCourse.name
          }}</text>
          <text class="course-progress">{{
            formatCourseProgress(currentCourse)
          }}</text>
        </view>
        <view class="course-selector">
          <picker
            :value="currentCourseIndex"
            :range="seriesDetail?.courses || []"
            range-key="volumeName"
            @change="onCourseChange"
          >
            <view class="selector-display">
              <text>{{ currentCourse.volumeName || "Level1" }}</text>
              <text class="arrow">▼</text>
            </view>
          </picker>
        </view>
      </view>

      <!-- 今日待学 -->
      <view v-if="todayLesson" class="today-section">
        <text class="section-title">今日待学</text>
        <view class="lesson-card today-lesson" @click="goToLesson(todayLesson)">
          <image
            class="lesson-icon"
            :src="
              todayLesson.lessonCoverUrl || '/static/images/default-lesson.png'
            "
            mode="aspectFill"
          />
          <view class="lesson-info">
            <text class="lesson-name">{{ todayLesson.lessonName }}</text>
            <text class="lesson-desc">{{ todayLesson.lessonDescription }}</text>
          </view>
          <view class="lesson-status">
            <text class="status-icon">📖</text>
          </view>
        </view>
      </view>

      <!-- 学习目录 -->
      <view class="lessons-section">
        <text class="section-title">学习目录</text>
        <view
          v-for="lesson in currentCourse.lessons"
          :key="lesson.id"
          class="lesson-card"
          :class="{
            completed: isLessonCompleted(lesson),
            locked: isLessonLocked(lesson),
          }"
          @click="goToLesson(lesson)"
        >
          <image
            class="lesson-icon"
            :src="lesson.lessonCoverUrl || '/static/images/default-lesson.png'"
            mode="aspectFill"
          />
          <view class="lesson-info">
            <text class="lesson-name">{{ lesson.lessonName }}</text>
            <text class="lesson-desc">{{ lesson.lessonDescription }}</text>
          </view>
          <view class="lesson-status">
            <text class="status-icon">{{ getLessonStatusIcon(lesson) }}</text>
          </view>
        </view>
      </view>
    </view>

    <view v-else class="error-container">
      <text>加载失败，请重试</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import {
  getCourseSeriesDetail,
  type CourseInfo,
  type CourseSeriesDetail,
  type LessonDetailInfo,
} from "@/api/homework";
import { onMounted, ref } from "vue";

interface Props {
  seriesId?: number;
}

interface Emits {
  (e: "course-click", course: CourseInfo): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(true);
const seriesDetail = ref<CourseSeriesDetail | null>(null);
const currentCourse = ref<CourseInfo | null>(null);
const currentCourseIndex = ref(0);
const todayLesson = ref<LessonDetailInfo | null>(null);
const seriesId = ref(1); // 默认使用系列ID 1

// 获取系列详情
const fetchSeriesDetail = async () => {
  try {
    loading.value = true;
    console.log("开始获取系列详情，seriesId:", seriesId.value);
    const res = await getCourseSeriesDetail(seriesId.value);
    console.log("series detail res", res);

    const data = res.data || res.getData();
    if (data) {
      seriesDetail.value = data;
      // 设置当前课程为第一个课程（默认课程）
      currentCourse.value =
        data.courses && data.courses.length > 0 ? data.courses[0] : null;
      currentCourseIndex.value = 0;

      // 设置今日待学课节
      if (currentCourse.value) {
        todayLesson.value = getTodayLesson(currentCourse.value);
      }

      console.log("系列详情获取成功:", data);
    } else {
      console.warn("系列详情数据为空");
      uni.showToast({
        title: "获取课程详情失败",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("获取课程详情失败:", error);
    uni.showToast({
      title: "获取课程详情失败",
      icon: "none",
    });
  } finally {
    loading.value = false;
  }
};

// 格式化课程进度
const formatCourseProgress = (course: CourseInfo): string => {
  if (!course.lessons) {
    return "已学0节/共0节";
  }

  const total = course.lessons.length;
  const completed = course.lessons.filter(
    (lesson: LessonDetailInfo) => lesson.autoUnlock === 0
  ).length;
  return `已学${completed}节/共${total}节`;
};

// 获取课程状态文本
const getCourseStatusText = (course: CourseInfo): string => {
  if (!course.lessons || course.lessons.length === 0) {
    return "未开始";
  }

  const total = course.lessons.length;
  const completed = course.lessons.filter(
    (lesson: LessonDetailInfo) => lesson.autoUnlock === 0
  ).length;

  if (completed === 0) {
    return "未开始";
  } else if (completed === total) {
    return "已完成";
  } else {
    return "学习中";
  }
};

// 获取今日待学课节（已解锁但未学习的第一个课节）
const getTodayLesson = (course: CourseInfo): LessonDetailInfo | null => {
  if (!course.lessons || course.lessons.length === 0) {
    return null;
  }

  // 找到第一个已解锁但未学习的课节作为今日待学
  // 这里假设 autoUnlock === 1 表示已解锁，需要根据实际业务逻辑调整
  const firstUnlearnedLesson = course.lessons.find(
    (lesson) => lesson.autoUnlock === 1 // 已解锁但未学习
  );

  return firstUnlearnedLesson || null;
};

// 课程切换
const onCourseChange = async (e: any) => {
  const index = e.detail.value;
  if (seriesDetail.value && seriesDetail.value.courses[index]) {
    const selectedCourse = seriesDetail.value.courses[index];

    // 重新调用接口获取指定课程的详情和lesson数据
    try {
      const res = await getCourseSeriesDetail(
        seriesId.value,
        selectedCourse.id
      );
      const data = res.data || res.getData();
      if (data) {
        seriesDetail.value = data;
        currentCourse.value =
          data.courses && data.courses.length > 0 ? data.courses[0] : null;
        currentCourseIndex.value = index;

        // 设置今日待学课节
        if (currentCourse.value) {
          todayLesson.value = getTodayLesson(currentCourse.value);
        }
      }
    } catch (error) {
      console.error("切换课程失败:", error);
      uni.showToast({
        title: "切换课程失败",
        icon: "none",
      });
    }
  }
};

// 判断课节是否已完成
const isLessonCompleted = (lesson: LessonDetailInfo): boolean => {
  // 这里根据实际业务逻辑判断，假设某个字段表示已完成
  return lesson.autoUnlock === 0; // 根据实际情况调整
};

// 判断课节是否被锁定
const isLessonLocked = (lesson: LessonDetailInfo): boolean => {
  // 这里根据实际业务逻辑判断，假设某个字段表示锁定状态
  return lesson.autoUnlock === 2; // 根据实际情况调整
};

// 获取课节状态图标
const getLessonStatusIcon = (lesson: LessonDetailInfo): string => {
  if (isLessonCompleted(lesson)) {
    return "✓"; // 已完成
  } else if (isLessonLocked(lesson)) {
    return "🔒"; // 锁定
  } else {
    return "📖"; // 可学习
  }
};

// 跳转到课节详情
const goToLesson = (lesson: LessonDetailInfo) => {
  if (isLessonLocked(lesson)) {
    uni.showToast({
      title: "课节尚未解锁",
      icon: "none",
    });
    return;
  }

  // 这里可以跳转到具体的课节学习页面
  uni.showToast({
    title: `开始学习：${lesson.lessonName}`,
    icon: "none",
  });
};

// 组件挂载时获取数据
onMounted(() => {
  // 设置系列ID
  if (props.seriesId) {
    seriesId.value = props.seriesId;
  }

  console.log(
    "course-series-list 组件已挂载，开始获取数据，seriesId:",
    seriesId.value
  );
  fetchSeriesDetail();
});

// 暴露刷新方法
const refresh = () => {
  fetchSeriesDetail();
};

defineExpose({
  refresh,
});
</script>

<style lang="scss" scoped>
.course-series-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;

  text {
    font-size: 16px;
    color: #666;
  }
}

.detail-content {
  padding: 20px;
}

.course-info-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .course-cover {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    margin-right: 15px;
  }

  .course-info {
    flex: 1;

    .course-name {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      display: block;
      margin-bottom: 5px;
    }

    .course-progress {
      font-size: 14px;
      color: #666;
      display: block;
    }
  }

  .course-selector {
    .selector-display {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      background: #f0f0f0;
      border-radius: 6px;
      min-width: 80px;

      text {
        font-size: 14px;
        color: #333;
      }

      .arrow {
        color: #999;
        margin-left: 8px;
      }
    }
  }
}

.today-section,
.lessons-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
  }
}

.lesson-card {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    opacity: 0.8;
  }

  .lesson-icon {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    margin-right: 12px;
  }

  .lesson-info {
    flex: 1;

    .lesson-name {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      display: block;
      margin-bottom: 4px;
    }

    .lesson-desc {
      font-size: 14px;
      color: #666;
      display: block;
    }
  }

  .lesson-status {
    .status-icon {
      font-size: 18px;
    }
  }

  &.completed {
    .lesson-info {
      opacity: 0.7;
    }

    .status-icon {
      color: #4caf50;
    }
  }

  &.locked {
    .lesson-info {
      opacity: 0.5;
    }

    .status-icon {
      color: #ccc;
    }
  }
}

.today-lesson {
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);

  .lesson-name {
    color: #2d3436 !important;
  }

  .lesson-desc {
    color: #636e72 !important;
  }
}
</style>
