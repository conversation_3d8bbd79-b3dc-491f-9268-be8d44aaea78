<template>
  <view class="study-page">
    <!-- 分类标签 -->
    <view class="study-tags">
      <view
        v-for="item in state.categoryList"
        :key="item.id"
        :class="['study-tag', { active: state.activeCategoryId === item.id }]"
        @click="handleCategoryClick(item)"
      >
        {{ item.name }}
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-area">
      <!-- 作业列表 -->
      <homework-list
        v-if="currentCategory?.type === 'homework'"
        ref="homeworkListRef"
        :homework-list="state.homeworkList"
      />

      <!-- 课程系列详情 -->
      <course-series-detail
        v-else-if="currentCategory?.type === 'series'"
        ref="courseSeriesDetailRef"
        :series-id="state.activeCategoryId"
      />
    </view>
  </view>
</template>

<script lang="ts" setup>
import {
  CourseCategoryItem,
  getHomeworkList,
  getMyCourseCategoryList,
} from "@/api/homework";
import { computed, onMounted, reactive, ref } from "vue";
// @ts-ignore
import CourseSeriesDetail from "@/pages/study/course-series-list.vue";
// @ts-ignore
import HomeworkList from "@/pages/study/homework-list.vue";
// @ts-ignore
import { onShareAppMessage, onShareTimeline } from "@dcloudio/uni-app";

// 组件引用
const homeworkListRef = ref();
const courseSeriesDetailRef = ref();

// 定义作业组接口
interface HomeworkGroup {
  year: number;
  month: number;
  homeworkList: any[];
}

const state = reactive({
  categoryList: [] as CourseCategoryItem[],
  activeCategoryId: 0, // 默认选中"我的作业"
  homeworkList: [] as HomeworkGroup[], // 作业列表数据
  loading: false,
});

// 计算当前选中的分类
const currentCategory = computed(() => {
  return state.categoryList.find((item) => item.id === state.activeCategoryId);
});

// 获取分类列表
const fetchCategoryList = async () => {
  try {
    const res = await getMyCourseCategoryList();
    const data = res.getData();
    if (Array.isArray(data)) {
      state.categoryList = data;
      // 如果当前没有选中的分类，则选中第一个分类
      if (state.categoryList.length > 0 && state.activeCategoryId === 0) {
        state.activeCategoryId = state.categoryList[0].id;
      }
    } else {
      state.categoryList = [];
    }
    return state.categoryList;
  } catch (error) {
    console.error("获取分类列表失败:", error);
    uni.showToast({
      title: "获取数据失败",
      icon: "none",
    });
    state.categoryList = [];
    return state.categoryList;
  }
};

// 获取作业列表
const fetchHomeworkList = async () => {
  try {
    state.loading = true;
    const res = await getHomeworkList({ pageNo: 1, pageSize: 500 });
    console.log("homework list res", res);

    const data = res.data || res.getData();
    if (Array.isArray(data)) {
      state.homeworkList = data;
    } else {
      state.homeworkList = [];
    }
  } catch (error) {
    console.error("获取作业列表失败:", error);
    uni.showToast({
      title: "获取作业数据失败",
      icon: "none",
    });
    state.homeworkList = [];
  } finally {
    state.loading = false;
  }
};

// 处理分类点击事件
const handleCategoryClick = async (category: CourseCategoryItem) => {
  if (state.activeCategoryId !== category.id) {
    state.activeCategoryId = category.id;

    // 如果是作业类型，获取作业列表
    if (category.type === "homework") {
      await fetchHomeworkList();
    }
  }
};

// 初始化数据
const initData = async () => {
  await fetchCategoryList();

  // 根据默认选中的分类获取相应数据
  const defaultCategory = state.categoryList.find(
    (item) => item.id === state.activeCategoryId
  );
  if (defaultCategory?.type === "homework") {
    await fetchHomeworkList();
  }
};

// 页面加载时初始化
onMounted(() => {
  initData();
});

// 分享给好友
// #ifdef MP-WEIXIN
onShareAppMessage(() => {
  return {
    title: "白熊作业广场",
    path: "/pages/homework-square/homework-square",
    imageUrl: "/static/icon.png",
  };
});

// 分享到朋友圈
onShareTimeline(() => {
  return {
    title: "白熊作业广场",
    query: "",
    imageUrl: "/static/icon.png",
  };
});
// #endif
</script>

<style lang="scss" scoped>
.study-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 0;

  .study-tags {
    display: flex;
    flex-wrap: wrap;
    padding: 12px 16px 4px;
    gap: 8px;

    .study-tag {
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 14px;
      background-color: #f0f0f0;
      color: #666;

      &.active {
        background-color: #ffe161;
        color: #333;
      }
    }
  }

  .content-area {
    flex: 1;
    min-height: 0;
  }

  .course-content {
    padding: 12px 16px;

    .course-switch {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 16px;

      .course-option {
        padding: 6px 12px;
        border-radius: 16px;
        font-size: 13px;
        background-color: #f0f0f0;
        color: #666;
        border: 1px solid #e0e0e0;

        &.active {
          background-color: #007aff;
          color: #fff;
          border-color: #007aff;
        }
      }
    }

    .course-detail {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;

      .course-info {
        text-align: center;

        .course-cover {
          width: 120px;
          height: 120px;
          border-radius: 8px;
          margin-bottom: 12px;
        }

        .course-name {
          display: block;
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }

        .course-desc {
          display: block;
          font-size: 14px;
          color: #666;
          line-height: 1.4;
        }
      }
    }

    .exercise-content {
      .today-task {
        background-color: #fff;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;

        .task-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;

          .task-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
          }

          .task-level {
            padding: 4px 8px;
            background-color: #ffe161;
            border-radius: 12px;
            font-size: 12px;
            color: #333;
          }
        }

        .task-progress {
          font-size: 14px;
          color: #666;
          margin-bottom: 12px;
        }

        .current-lesson {
          .lesson-name {
            display: block;
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
          }

          .lesson-title {
            display: block;
            font-size: 13px;
            color: #666;
          }
        }
      }

      .lesson-list {
        background-color: #fff;
        border-radius: 8px;
        padding: 16px;

        .section-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          margin-bottom: 12px;
        }

        .lesson-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          &.completed {
            .lesson-info {
              opacity: 0.6;
            }

            .status-icon {
              color: #4caf50;
            }
          }

          .lesson-info {
            flex: 1;

            .lesson-name {
              display: block;
              font-size: 14px;
              font-weight: bold;
              color: #333;
              margin-bottom: 4px;
            }

            .lesson-title {
              display: block;
              font-size: 13px;
              color: #666;
            }
          }

          .lesson-status {
            .status-icon {
              font-size: 16px;
              color: #ccc;
            }
          }
        }
      }
    }
  }

  .homework-list {
    padding: 12px 16px;

    .month-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin: 8px 0;
    }

    .homework-item {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;

      .homework-info {
        .title-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .left {
            display: flex;
            align-items: flex-start;
            flex: 1;

            .iconfont {
              font-size: 20px;
              color: #fff200;
              margin-right: 8px;
              margin-top: 2px;
            }

            .title {
              font-size: 15px;
              color: #333;
              line-height: 1.4;
            }
          }

          .status {
            padding: 4px 12px;
            border-radius: 24px;
            font-size: 13px;

            &.submitted {
              background-color: #f0f0f0;
              color: #999999;
            }

            &.unsubmitted {
              background-color: #ffe161;
              color: #333333;
            }
          }
        }

        .date {
          font-size: 13px;
          color: #999;
          padding-left: 28px;
        }
      }
    }
  }
}
</style>
