<template>
  <view class="course-series-detail-page">
    <view v-if="state.loading" class="loading-container">
      <text>加载中...</text>
    </view>

    <view v-else-if="state.seriesDetail" class="detail-content">
      <!-- 课程信息卡片 -->
      <view class="course-info-card">
        <image
          class="course-cover"
          :src="state.currentCourse.coverUrl"
          mode="aspectFill"
        />
        <view class="course-info">
          <text class="course-name">{{ state.currentCourse.name }}</text>
          <text class="course-progress">{{ formatProgress() }}</text>
          <view class="course-selector">
            <picker
              :value="state.currentCourseIndex"
              :range="state.seriesDetail.courses"
              range-key="volumeName"
              @change="onCourseChange"
            >
              <view class="selector-display">
                <text>{{ state.currentCourse.volumeName || "选择课程" }}</text>
                <text class="arrow">▼</text>
              </view>
            </picker>
          </view>
        </view>
      </view>

      <!-- 今日待学 -->
      <view class="today-section">
        <view class="section-title">今日待学</view>
        <view v-if="state.todayLesson" class="lesson-card today-lesson">
          <image
            class="lesson-icon"
            :src="
              state.todayLesson.lessonCoverUrl ||
              '/static/images/default-lesson.png'
            "
            mode="aspectFill"
          />
          <view class="lesson-info">
            <text class="lesson-name">{{ state.todayLesson.lessonName }}</text>
            <text class="lesson-desc">{{
              state.todayLesson.lessonDescription
            }}</text>
          </view>
          <view class="lesson-status">
            <text class="status-icon">🔒</text>
          </view>
        </view>
      </view>

      <!-- 学习目录 -->
      <view class="lessons-section">
        <view class="section-title">学习目录</view>
        <view class="lessons-list">
          <view
            v-for="lesson in state.currentCourse.lessons"
            :key="lesson.id"
            class="lesson-card"
            @click="goToLesson(lesson)"
          >
            <image
              class="lesson-icon"
              :src="
                lesson.lessonCoverUrl || '/static/images/default-lesson.png'
              "
              mode="aspectFill"
            />
            <view class="lesson-info">
              <text class="lesson-name">{{ lesson.lessonName }}</text>
              <text class="lesson-desc">{{ lesson.lessonDescription }}</text>
            </view>
            <view class="lesson-status">
              <text v-if="lesson.autoUnlock === 1" class="status-icon">🔒</text>
              <text v-else class="status-icon">✓</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view v-else class="error-container">
      <text>加载失败，请重试</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import {
  getCourseSeriesDetail,
  type CourseInfo,
  type CourseSeriesDetail,
  type LessonDetailInfo,
} from "@/api/homework";
import { reactive } from "vue";
// @ts-ignore
import { onLoad } from "@dcloudio/uni-app";

const state = reactive({
  seriesDetail: null as CourseSeriesDetail | null,
  currentCourse: null as CourseInfo | null,
  currentCourseIndex: 0,
  todayLesson: null as LessonDetailInfo | null,
  loading: true,
  seriesId: 0,
});

// 页面加载时获取系列ID
// @ts-ignore
onLoad((options: Record<string, any>) => {
  if (options.seriesId) {
    state.seriesId = parseInt(options.seriesId);
    fetchSeriesDetail();
  } else {
    uni.showToast({
      title: "系列ID无效",
      icon: "none",
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 获取系列详情
const fetchSeriesDetail = async () => {
  try {
    state.loading = true;
    const res = await getCourseSeriesDetail(state.seriesId);
    console.log("series detail res", res);
    if (res.data) {
      state.seriesDetail = res.data;
      // 设置当前课程为第一个课程（默认课程）
      state.currentCourse =
        res.data.courses && res.data.courses.length > 0
          ? res.data.courses[0]
          : null;
      state.currentCourseIndex = 0;

      // 设置今日待学课节（第一个未完成的课节）
      if (
        state.currentCourse &&
        state.currentCourse.lessons &&
        state.currentCourse.lessons.length > 0
      ) {
        state.todayLesson = state.currentCourse.lessons[0];
      }
    } else {
      uni.showToast({
        title: "获取课程详情失败",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("获取课程详情失败:", error);
    uni.showToast({
      title: "获取课程详情失败",
      icon: "none",
    });
  } finally {
    state.loading = false;
  }
};

// 课程切换
const onCourseChange = (e: any) => {
  const index = e.detail.value;
  if (state.seriesDetail && state.seriesDetail.courses[index]) {
    state.currentCourseIndex = index;
    state.currentCourse = state.seriesDetail.courses[index];

    // 更新今日待学课节
    if (state.currentCourse.lessons && state.currentCourse.lessons.length > 0) {
      state.todayLesson = state.currentCourse.lessons[0];
    }
  }
};

// 格式化进度
const formatProgress = () => {
  if (!state.currentCourse || !state.currentCourse.lessons) {
    return "已学0节/共0节";
  }

  const total = state.currentCourse.lessons.length;
  const completed = state.currentCourse.lessons.filter(
    (lesson) => lesson.autoUnlock === 0
  ).length;
  return `已学${completed}节/共${total}节`;
};

// 跳转到课节详情
const goToLesson = (lesson: LessonDetailInfo) => {
  // 这里可以跳转到具体的课节学习页面
  uni.showToast({
    title: `开始学习：${lesson.lessonName}`,
    icon: "none",
  });
};
</script>

<style lang="scss" scoped>
.course-series-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;

  text {
    font-size: 16px;
    color: #666;
  }
}

.detail-content {
  padding: 20px;
}

.course-info-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .course-cover {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    margin-right: 15px;
  }

  .course-info {
    flex: 1;

    .course-name {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      display: block;
      margin-bottom: 5px;
    }

    .course-progress {
      font-size: 14px;
      color: #666;
      display: block;
      margin-bottom: 10px;
    }

    .course-selector {
      .selector-display {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        background: #f0f0f0;
        border-radius: 6px;

        text {
          font-size: 14px;
          color: #333;
        }

        .arrow {
          color: #999;
        }
      }
    }
  }
}

.today-section,
.lessons-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
  }
}

.lesson-card {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

  .lesson-icon {
    width: 50px;
    height: 50px;
    border-radius: 6px;
    margin-right: 12px;
  }

  .lesson-info {
    flex: 1;

    .lesson-name {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      display: block;
      margin-bottom: 4px;
    }

    .lesson-desc {
      font-size: 14px;
      color: #666;
      display: block;
    }
  }

  .lesson-status {
    .status-icon {
      font-size: 18px;
    }
  }
}

.today-lesson {
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);

  .lesson-name {
    color: #2d3436 !important;
  }

  .lesson-desc {
    color: #636e72 !important;
  }
}
</style>
